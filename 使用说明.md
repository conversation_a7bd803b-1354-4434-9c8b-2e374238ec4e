# Outlook VBA 自动定时发送草稿邮件 - 完整模块使用说明

## 🎯 功能概述

这是一个**完整的单模块解决方案**，可以自动定时发送Microsoft Outlook 2016草稿箱中的邮件：

### ✨ 核心特性
- ✅ **一键启动/停止** - 简单的启动和停止控制
- ✅ **智能定时发送** - 可配置的检查间隔（1分钟-24小时）
- ✅ **工作时间控制** - 可设置只在指定时间段发送
- ✅ **完整错误处理** - 发送失败的邮件保留在草稿箱
- ✅ **详细日志记录** - 所有操作都有完整的日志追踪
- ✅ **状态监控** - 实时查看运行状态和统计信息
- ✅ **安全验证** - 自动跳过无收件人或无效邮件
- ✅ **手动发送** - 支持立即发送所有草稿
- ✅ **模块测试** - 内置测试功能验证模块正常工作

### 🔧 主要功能函数
| 函数名 | 功能说明 | 使用场景 |
|--------|----------|----------|
| `StartAutoSendDrafts` | 启动自动发送定时器 | 开始自动发送功能 |
| `StopAutoSendDrafts` | 停止自动发送定时器 | 停止自动发送功能 |
| `ConfigureSettings` | 配置所有参数设置 | 设置时间间隔、工作时间等 |
| `ManualSendDrafts` | 手动发送一次草稿 | 立即发送所有草稿邮件 |
| `ShowStatus` | 显示当前运行状态 | 查看系统状态和统计信息 |
| `TestModule` | 测试模块功能 | 验证模块是否正常工作 |

## 安装步骤

### 1. 启用Outlook宏功能

1. 打开 **Microsoft Outlook 2016**
2. 点击 **文件** → **选项**
3. 选择 **信任中心** → **信任中心设置**
4. 选择 **宏设置**
5. 选择 **启用所有宏** 或 **有关所有宏的通知**
6. 点击 **确定** 保存设置

### 2. 导入VBA代码

1. 在Outlook中按 **Alt + F11** 打开VBA编辑器
2. 在项目资源管理器中右键点击 **Microsoft Outlook Objects**
3. 选择 **插入** → **模块**
4. 将 `OutlookAutoSendDrafts.bas` 文件中的所有代码复制到新模块中
5. 按 **Ctrl + S** 保存

### 3. 创建快捷按钮（可选）

1. 在Outlook中右键点击功能区
2. 选择 **自定义功能区**
3. 在右侧选择一个选项卡（如"开始"）
4. 点击 **新建组**
5. 在左侧选择 **宏**，找到以下宏：
   - `StartAutoSendDrafts`
   - `StopAutoSendDrafts`
   - `ManualSendDrafts`
   - `ConfigureSettings`
6. 将这些宏添加到新建的组中

## 使用方法

### 基本操作

#### 1. 配置设置
```vba
' 在VBA编辑器中运行，或通过功能区按钮
ConfigureSettings
```
- 设置检查间隔（分钟）
- 选择是否只在工作时间发送
- 选择是否启用日志记录

#### 2. 启动自动发送
```vba
StartAutoSendDrafts
```
- 启动定时器
- 开始自动检查和发送草稿

#### 3. 停止自动发送
```vba
StopAutoSendDrafts
```
- 停止定时器
- 停止自动发送功能

#### 4. 手动发送一次
```vba
ManualSendDrafts
```
- 立即检查并发送所有草稿
- 不影响定时器状态

### 高级配置

#### 修改默认设置
在VBA代码中可以修改以下常量：

```vba
Private Const DEFAULT_INTERVAL_MINUTES As Long = 5  ' 默认检查间隔
Private Const LOG_FILE_PATH As String = "C:\Temp\OutlookAutoSend.log"  ' 日志文件路径
```

#### 工作时间设置
默认工作时间为 9:00-18:00，可以在 `IsWorkingHours()` 函数中修改：

```vba
Private Function IsWorkingHours() As Boolean
    Dim currentHour As Integer
    currentHour = Hour(Now)
    IsWorkingHours = (currentHour >= 9 And currentHour < 18)  ' 修改这里
End Function
```

## 安全注意事项

### 1. 草稿邮件检查
- 确保草稿邮件有正确的收件人
- 检查邮件内容和附件
- 无收件人的邮件会被跳过

### 2. 错误处理
- 发送失败的邮件会保留在草稿箱
- 所有操作都有详细的日志记录
- 错误信息会显示在消息框中

### 3. 性能考虑
- 建议检查间隔不少于1分钟
- 大量草稿邮件可能影响Outlook性能
- 可以通过日志监控发送状态

## 故障排除

### 常见问题

#### 1. 宏无法运行
**解决方案：**
- 检查宏安全设置
- 确保Outlook正在运行
- 重启Outlook并重新加载宏

#### 2. 邮件发送失败
**可能原因：**
- 网络连接问题
- 邮件服务器设置错误
- 收件人地址无效
- 附件过大

**解决方案：**
- 检查网络连接
- 验证邮件账户设置
- 检查草稿邮件内容

#### 3. 定时器不工作
**解决方案：**
- 重新启动定时器
- 检查VBA代码是否完整
- 确保没有其他宏冲突

#### 4. 日志文件无法创建
**解决方案：**
- 确保 `C:\Temp` 目录存在
- 检查文件写入权限
- 修改日志文件路径

### 调试方法

1. **查看日志文件**
   - 位置：`C:\Temp\OutlookAutoSend.log`
   - 包含详细的操作记录和错误信息

2. **VBA调试**
   - 在VBA编辑器中设置断点
   - 使用 `Debug.Print` 输出调试信息
   - 检查变量值和执行流程

3. **测试模式**
   - 先用少量草稿邮件测试
   - 设置较短的检查间隔进行测试
   - 验证所有功能正常后再正式使用

## 版本信息

- **版本：** 1.0
- **兼容性：** Microsoft Outlook 2016
- **创建日期：** 2025-08-12
- **语言：** VBA (Visual Basic for Applications)

## 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. Outlook事件查看器
3. VBA编辑器中的错误提示

---

**重要提醒：** 使用前请务必备份重要邮件，并在测试环境中验证功能正常。
