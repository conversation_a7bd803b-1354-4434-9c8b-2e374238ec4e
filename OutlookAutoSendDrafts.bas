' ========================================================================
' Outlook VBA - 自动定时发送草稿邮件
' 兼容 Microsoft Outlook 2016
' 作者：AI Assistant
' 创建日期：2025-08-12
' ========================================================================

Option Explicit

' ========================================================================
' 全局变量声明
' ========================================================================
Private WithEvents objOutlookApp As Outlook.Application
Private objNamespace As Outlook.NameSpace
Private objDraftsFolder As Outlook.MAPIFolder
Private objSentItemsFolder As Outlook.MAPIFolder

' 定时器相关变量
Private TimerID As Long
Private IsTimerRunning As Boolean

' 配置变量
Private Const DEFAULT_INTERVAL_MINUTES As Long = 5  ' 默认检查间隔（分钟）
Private IntervalMinutes As Long
Private EnableWorkingHoursOnly As Boolean
Private EnableLogging As Boolean

' 日志文件路径
Private Const LOG_FILE_PATH As String = "C:\Temp\OutlookAutoSend.log"

' ========================================================================
' 公共接口函数
' ========================================================================

' 启动自动发送定时器
Public Sub StartAutoSendDrafts()
    On Error GoTo ErrorHandler
    
    ' 初始化配置
    Call InitializeSettings
    
    ' 初始化Outlook对象
    If Not InitializeOutlookObjects() Then
        MsgBox "无法初始化Outlook对象，请确保Outlook正在运行。", vbCritical, "错误"
        Exit Sub
    End If
    
    ' 启动定时器
    If Not IsTimerRunning Then
        Call StartTimer
        MsgBox "自动发送定时器已启动。检查间隔：" & IntervalMinutes & " 分钟", vbInformation, "定时器启动"
        Call WriteLog("自动发送定时器已启动，间隔：" & IntervalMinutes & " 分钟")
    Else
        MsgBox "定时器已经在运行中。", vbInformation, "提示"
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "启动定时器时发生错误：" & Err.Description, vbCritical, "错误"
    Call WriteLog("启动定时器错误：" & Err.Description)
End Sub

' 停止自动发送定时器
Public Sub StopAutoSendDrafts()
    On Error GoTo ErrorHandler
    
    If IsTimerRunning Then
        Call StopTimer
        MsgBox "自动发送定时器已停止。", vbInformation, "定时器停止"
        Call WriteLog("自动发送定时器已停止")
    Else
        MsgBox "定时器当前未运行。", vbInformation, "提示"
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "停止定时器时发生错误：" & Err.Description, vbCritical, "错误"
    Call WriteLog("停止定时器错误：" & Err.Description)
End Sub

' 手动执行一次草稿发送
Public Sub ManualSendDrafts()
    On Error GoTo ErrorHandler
    
    ' 初始化Outlook对象
    If Not InitializeOutlookObjects() Then
        MsgBox "无法初始化Outlook对象，请确保Outlook正在运行。", vbCritical, "错误"
        Exit Sub
    End If
    
    ' 执行发送
    Call ProcessDraftEmails
    
    Exit Sub
    
ErrorHandler:
    MsgBox "手动发送草稿时发生错误：" & Err.Description, vbCritical, "错误"
    Call WriteLog("手动发送草稿错误：" & Err.Description)
End Sub

' 配置设置
Public Sub ConfigureSettings()
    Dim userInput As String
    Dim newInterval As Long
    
    ' 获取用户输入的时间间隔
    userInput = InputBox("请输入检查间隔（分钟）：", "配置设置", CStr(DEFAULT_INTERVAL_MINUTES))
    
    If userInput <> "" And IsNumeric(userInput) Then
        newInterval = CLng(userInput)
        If newInterval > 0 And newInterval <= 1440 Then ' 最大24小时
            IntervalMinutes = newInterval
            
            ' 询问是否只在工作时间发送
            Dim workingHoursResponse As VbMsgBoxResult
            workingHoursResponse = MsgBox("是否只在工作时间（9:00-18:00）发送邮件？", vbYesNo + vbQuestion, "工作时间设置")
            EnableWorkingHoursOnly = (workingHoursResponse = vbYes)
            
            ' 询问是否启用日志记录
            Dim loggingResponse As VbMsgBoxResult
            loggingResponse = MsgBox("是否启用日志记录？", vbYesNo + vbQuestion, "日志设置")
            EnableLogging = (loggingResponse = vbYes)
            
            MsgBox "设置已保存：" & vbCrLf & _
                   "检查间隔：" & IntervalMinutes & " 分钟" & vbCrLf & _
                   "仅工作时间：" & IIf(EnableWorkingHoursOnly, "是", "否") & vbCrLf & _
                   "日志记录：" & IIf(EnableLogging, "是", "否"), vbInformation, "设置完成"
        Else
            MsgBox "请输入1到1440之间的数值（分钟）。", vbExclamation, "输入错误"
        End If
    End If
End Sub

' ========================================================================
' 内部功能函数
' ========================================================================

' 初始化设置
Private Sub InitializeSettings()
    IntervalMinutes = DEFAULT_INTERVAL_MINUTES
    EnableWorkingHoursOnly = False
    EnableLogging = True
End Sub

' 初始化Outlook对象
Private Function InitializeOutlookObjects() As Boolean
    On Error GoTo ErrorHandler
    
    ' 获取Outlook应用程序对象
    Set objOutlookApp = Application
    Set objNamespace = objOutlookApp.GetNamespace("MAPI")
    
    ' 获取草稿箱和已发送邮件文件夹
    Set objDraftsFolder = objNamespace.GetDefaultFolder(olFolderDrafts)
    Set objSentItemsFolder = objNamespace.GetDefaultFolder(olFolderSentMail)
    
    InitializeOutlookObjects = True
    Exit Function
    
ErrorHandler:
    InitializeOutlookObjects = False
    Call WriteLog("初始化Outlook对象错误：" & Err.Description)
End Function

' 启动定时器
Private Sub StartTimer()
    ' 使用Windows API SetTimer函数
    TimerID = SetTimer(0, 0, IntervalMinutes * 60 * 1000, AddressOf TimerProc)
    IsTimerRunning = (TimerID <> 0)
End Sub

' 停止定时器
Private Sub StopTimer()
    If TimerID <> 0 Then
        Call KillTimer(0, TimerID)
        TimerID = 0
    End If
    IsTimerRunning = False
End Sub

' 处理草稿邮件
Private Sub ProcessDraftEmails()
    On Error GoTo ErrorHandler
    
    Dim draftItems As Outlook.Items
    Dim mailItem As Outlook.MailItem
    Dim i As Long
    Dim sentCount As Long
    Dim errorCount As Long
    
    ' 检查工作时间限制
    If EnableWorkingHoursOnly And Not IsWorkingHours() Then
        Call WriteLog("当前不在工作时间，跳过发送")
        Exit Sub
    End If
    
    Set draftItems = objDraftsFolder.Items
    sentCount = 0
    errorCount = 0
    
    Call WriteLog("开始处理草稿邮件，共 " & draftItems.Count & " 封")
    
    ' 从后往前遍历，避免索引问题
    For i = draftItems.Count To 1 Step -1
        If TypeOf draftItems(i) Is Outlook.MailItem Then
            Set mailItem = draftItems(i)
            
            ' 尝试发送邮件
            If SendSingleEmail(mailItem) Then
                sentCount = sentCount + 1
            Else
                errorCount = errorCount + 1
            End If
        End If
    Next i
    
    ' 记录结果
    Dim resultMsg As String
    resultMsg = "草稿处理完成。成功发送：" & sentCount & " 封，失败：" & errorCount & " 封"
    Call WriteLog(resultMsg)
    
    If sentCount > 0 Or errorCount > 0 Then
        MsgBox resultMsg, vbInformation, "处理结果"
    End If
    
    Exit Sub
    
ErrorHandler:
    Call WriteLog("处理草稿邮件错误：" & Err.Description)
End Sub

' 发送单封邮件
Private Function SendSingleEmail(ByRef mailItem As Outlook.MailItem) As Boolean
    On Error GoTo ErrorHandler
    
    ' 验证邮件基本信息
    If Trim(mailItem.To) = "" And Trim(mailItem.CC) = "" And Trim(mailItem.BCC) = "" Then
        Call WriteLog("跳过无收件人的邮件：" & mailItem.Subject)
        SendSingleEmail = False
        Exit Function
    End If
    
    ' 发送邮件
    mailItem.Send
    
    Call WriteLog("成功发送邮件：" & mailItem.Subject & " -> " & mailItem.To)
    SendSingleEmail = True
    Exit Function
    
ErrorHandler:
    Call WriteLog("发送邮件失败：" & mailItem.Subject & " - " & Err.Description)
    SendSingleEmail = False
End Function

' 检查是否在工作时间
Private Function IsWorkingHours() As Boolean
    Dim currentHour As Integer
    currentHour = Hour(Now)
    IsWorkingHours = (currentHour >= 9 And currentHour < 18)
End Function

' 写入日志
Private Sub WriteLog(ByVal message As String)
    If Not EnableLogging Then Exit Sub
    
    On Error Resume Next
    
    Dim fileNum As Integer
    Dim logMessage As String
    
    fileNum = FreeFile
    logMessage = Format(Now, "yyyy-mm-dd hh:mm:ss") & " - " & message
    
    ' 确保日志目录存在
    If Dir("C:\Temp", vbDirectory) = "" Then
        MkDir "C:\Temp"
    End If
    
    Open LOG_FILE_PATH For Append As #fileNum
    Print #fileNum, logMessage
    Close #fileNum
End Sub

' ========================================================================
' Windows API 声明（用于定时器）
' ========================================================================
#If VBA7 Then
    Private Declare PtrSafe Function SetTimer Lib "user32" (ByVal hwnd As LongPtr, ByVal nIDEvent As LongPtr, ByVal uElapse As Long, ByVal lpTimerFunc As LongPtr) As LongPtr
    Private Declare PtrSafe Function KillTimer Lib "user32" (ByVal hwnd As LongPtr, ByVal nIDEvent As LongPtr) As Long
#Else
    Private Declare Function SetTimer Lib "user32" (ByVal hwnd As Long, ByVal nIDEvent As Long, ByVal uElapse As Long, ByVal lpTimerFunc As Long) As Long
    Private Declare Function KillTimer Lib "user32" (ByVal hwnd As Long, ByVal nIDEvent As Long) As Long
#End If

' 定时器回调函数
#If VBA7 Then
    Public Sub TimerProc(ByVal hwnd As LongPtr, ByVal uMsg As Long, ByVal idEvent As LongPtr, ByVal dwTime As Long)
#Else
    Public Sub TimerProc(ByVal hwnd As Long, ByVal uMsg As Long, ByVal idEvent As Long, ByVal dwTime As Long)
#End If
    ' 在定时器回调中执行草稿发送
    Call ProcessDraftEmails
End Sub

' ========================================================================
' 清理函数
' ========================================================================
Private Sub Class_Terminate()
    ' 确保定时器被清理
    Call StopTimer
    
    ' 清理对象引用
    Set objOutlookApp = Nothing
    Set objNamespace = Nothing
    Set objDraftsFolder = Nothing
    Set objSentItemsFolder = Nothing
End Sub
