' ========================================================================
' Outlook VBA - 自动定时发送草稿邮件 - 完整模块
' 兼容 Microsoft Outlook 2016
' 作者：AI Assistant
' 创建日期：2025-08-12
'
' 功能说明：
' 1. 自动定时发送草稿箱中的邮件
' 2. 可配置发送间隔和工作时间限制
' 3. 完整的错误处理和日志记录
' 4. 启动/停止/手动发送控制
'
' 使用方法：
' - 运行 StartAutoSendDrafts 启动自动发送
' - 运行 StopAutoSendDrafts 停止自动发送
' - 运行 ConfigureSettings 配置参数
' - 运行 ManualSendDrafts 手动发送一次
' ========================================================================

Option Explicit

' ========================================================================
' Windows API 声明（用于定时器功能）
' ========================================================================
#If VBA7 Then
    Private Declare PtrSafe Function SetTimer Lib "user32" (ByVal hwnd As LongPtr, ByVal nIDEvent As LongPtr, ByVal uElapse As Long, ByVal lpTimerFunc As LongPtr) As LongPtr
    Private Declare PtrSafe Function KillTimer Lib "user32" (ByVal hwnd As LongPtr, ByVal nIDEvent As LongPtr) As Long
    Private Declare PtrSafe Function GetTickCount Lib "kernel32" () As Long
#Else
    Private Declare Function SetTimer Lib "user32" (ByVal hwnd As Long, ByVal nIDEvent As Long, ByVal uElapse As Long, ByVal lpTimerFunc As Long) As Long
    Private Declare Function KillTimer Lib "user32" (ByVal hwnd As Long, ByVal nIDEvent As Long) As Long
    Private Declare Function GetTickCount Lib "kernel32" () As Long
#End If

' ========================================================================
' 模块级变量声明
' ========================================================================
Private objOutlookApp As Outlook.Application
Private objNamespace As Outlook.NameSpace
Private objDraftsFolder As Outlook.MAPIFolder
Private objSentItemsFolder As Outlook.MAPIFolder

' 定时器相关变量
Private TimerID As Long
Private IsTimerRunning As Boolean
Private LastCheckTime As Date

' 配置变量
Private Const DEFAULT_INTERVAL_MINUTES As Long = 5  ' 默认检查间隔（分钟）
Private IntervalMinutes As Long
Private EnableWorkingHoursOnly As Boolean
Private EnableLogging As Boolean
Private WorkingHourStart As Integer
Private WorkingHourEnd As Integer

' 日志和统计变量
Private Const LOG_FILE_PATH As String = "C:\Temp\OutlookAutoSend.log"
Private TotalEmailsSent As Long
Private TotalEmailsFailed As Long

' ========================================================================
' 主要公共接口函数
' ========================================================================

' 启动自动发送定时器
Public Sub StartAutoSendDrafts()
    On Error GoTo ErrorHandler

    ' 显示启动确认对话框
    Dim response As VbMsgBoxResult
    response = MsgBox("确定要启动自动发送草稿邮件功能吗？" & vbCrLf & vbCrLf & _
                     "当前设置：" & vbCrLf & _
                     "检查间隔：" & GetIntervalMinutes() & " 分钟" & vbCrLf & _
                     "工作时间限制：" & IIf(GetWorkingHoursOnly(), "是", "否") & vbCrLf & _
                     "日志记录：" & IIf(GetLoggingEnabled(), "是", "否"), _
                     vbYesNo + vbQuestion, "启动自动发送")

    If response = vbNo Then Exit Sub

    ' 初始化所有设置和对象
    Call InitializeModule

    ' 检查初始化是否成功
    If Not InitializeOutlookObjects() Then
        MsgBox "无法初始化Outlook对象，请确保Outlook正在运行。", vbCritical, "初始化失败"
        Exit Sub
    End If

    ' 启动定时器
    If Not IsTimerRunning Then
        Call StartTimer
        LastCheckTime = Now
        Call WriteLog("=== 自动发送定时器已启动 ===")
        Call WriteLog("检查间隔：" & IntervalMinutes & " 分钟")
        Call WriteLog("工作时间限制：" & IIf(EnableWorkingHoursOnly, "是 (" & WorkingHourStart & ":00-" & WorkingHourEnd & ":00)", "否"))

        MsgBox "自动发送定时器已成功启动！" & vbCrLf & vbCrLf & _
               "检查间隔：" & IntervalMinutes & " 分钟" & vbCrLf & _
               "下次检查时间：" & Format(DateAdd("n", IntervalMinutes, Now), "yyyy-mm-dd hh:mm:ss") & vbCrLf & vbCrLf & _
               "可以通过运行 StopAutoSendDrafts 来停止。", vbInformation, "启动成功"
    Else
        MsgBox "定时器已经在运行中。" & vbCrLf & _
               "上次检查时间：" & Format(LastCheckTime, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
               "下次检查时间：" & Format(DateAdd("n", IntervalMinutes, LastCheckTime), "yyyy-mm-dd hh:mm:ss"), vbInformation, "运行状态"
    End If

    Exit Sub

ErrorHandler:
    MsgBox "启动定时器时发生错误：" & Err.Description & vbCrLf & "错误号：" & Err.Number, vbCritical, "启动错误"
    Call WriteLog("启动定时器错误：" & Err.Description & " (错误号：" & Err.Number & ")")
End Sub

' 停止自动发送定时器
Public Sub StopAutoSendDrafts()
    On Error GoTo ErrorHandler

    If IsTimerRunning Then
        Call StopTimer
        Call WriteLog("=== 自动发送定时器已停止 ===")
        Call WriteLog("运行统计 - 成功发送：" & TotalEmailsSent & " 封，失败：" & TotalEmailsFailed & " 封")

        MsgBox "自动发送定时器已停止。" & vbCrLf & vbCrLf & _
               "运行统计：" & vbCrLf & _
               "成功发送：" & TotalEmailsSent & " 封" & vbCrLf & _
               "发送失败：" & TotalEmailsFailed & " 封", vbInformation, "停止成功"

        ' 重置统计
        TotalEmailsSent = 0
        TotalEmailsFailed = 0
    Else
        MsgBox "定时器当前未运行。", vbInformation, "状态提示"
    End If

    Exit Sub

ErrorHandler:
    MsgBox "停止定时器时发生错误：" & Err.Description, vbCritical, "停止错误"
    Call WriteLog("停止定时器错误：" & Err.Description)
End Sub

' 手动执行一次草稿发送
Public Sub ManualSendDrafts()
    On Error GoTo ErrorHandler

    ' 显示确认对话框
    Dim response As VbMsgBoxResult
    response = MsgBox("确定要立即发送所有草稿邮件吗？", vbYesNo + vbQuestion, "手动发送确认")
    If response = vbNo Then Exit Sub

    ' 初始化对象
    If Not InitializeOutlookObjects() Then
        MsgBox "无法初始化Outlook对象，请确保Outlook正在运行。", vbCritical, "初始化失败"
        Exit Sub
    End If

    ' 执行发送
    Call WriteLog("=== 手动发送草稿邮件开始 ===")
    Call ProcessDraftEmails
    Call WriteLog("=== 手动发送草稿邮件结束 ===")

    Exit Sub

ErrorHandler:
    MsgBox "手动发送草稿时发生错误：" & Err.Description, vbCritical, "发送错误"
    Call WriteLog("手动发送草稿错误：" & Err.Description)
End Sub

' 配置设置（完整配置界面）
Public Sub ConfigureSettings()
    On Error GoTo ErrorHandler

    Dim userInput As String
    Dim newInterval As Long
    Dim startHour As String, endHour As String

    ' 显示当前设置
    MsgBox "当前设置：" & vbCrLf & _
           "检查间隔：" & GetIntervalMinutes() & " 分钟" & vbCrLf & _
           "工作时间限制：" & IIf(GetWorkingHoursOnly(), "是 (" & GetWorkingHourStart() & ":00-" & GetWorkingHourEnd() & ":00)", "否") & vbCrLf & _
           "日志记录：" & IIf(GetLoggingEnabled(), "是", "否"), vbInformation, "当前配置"

    ' 1. 配置检查间隔
    userInput = InputBox("请输入检查间隔（分钟）：" & vbCrLf & vbCrLf & _
                        "建议值：" & vbCrLf & _
                        "• 1-5分钟：频繁检查" & vbCrLf & _
                        "• 10-30分钟：正常使用" & vbCrLf & _
                        "• 60分钟以上：低频检查", "时间间隔设置", CStr(GetIntervalMinutes()))

    If userInput <> "" And IsNumeric(userInput) Then
        newInterval = CLng(userInput)
        If newInterval >= 1 And newInterval <= 1440 Then ' 1分钟到24小时
            IntervalMinutes = newInterval
        Else
            MsgBox "时间间隔必须在1到1440分钟之间！", vbExclamation, "输入错误"
            Exit Sub
        End If
    Else
        Exit Sub ' 用户取消
    End If

    ' 2. 配置工作时间限制
    Dim workingHoursResponse As VbMsgBoxResult
    workingHoursResponse = MsgBox("是否只在工作时间发送邮件？" & vbCrLf & vbCrLf & _
                                 "选择'是'：只在指定时间段发送" & vbCrLf & _
                                 "选择'否'：24小时都可以发送", vbYesNo + vbQuestion, "工作时间限制")

    EnableWorkingHoursOnly = (workingHoursResponse = vbYes)

    ' 3. 如果启用工作时间限制，配置具体时间
    If EnableWorkingHoursOnly Then
        startHour = InputBox("请输入工作开始时间（小时，0-23）：", "工作开始时间", "9")
        If IsNumeric(startHour) And CLng(startHour) >= 0 And CLng(startHour) <= 23 Then
            WorkingHourStart = CLng(startHour)
        Else
            WorkingHourStart = 9 ' 默认值
        End If

        endHour = InputBox("请输入工作结束时间（小时，0-23）：", "工作结束时间", "18")
        If IsNumeric(endHour) And CLng(endHour) >= 0 And CLng(endHour) <= 23 Then
            WorkingHourEnd = CLng(endHour)
        Else
            WorkingHourEnd = 18 ' 默认值
        End If

        ' 验证时间设置
        If WorkingHourStart >= WorkingHourEnd Then
            MsgBox "工作开始时间不能大于或等于结束时间！使用默认设置 9:00-18:00", vbExclamation, "时间设置错误"
            WorkingHourStart = 9
            WorkingHourEnd = 18
        End If
    End If

    ' 4. 配置日志记录
    Dim loggingResponse As VbMsgBoxResult
    loggingResponse = MsgBox("是否启用详细日志记录？" & vbCrLf & vbCrLf & _
                            "日志文件位置：" & LOG_FILE_PATH & vbCrLf & vbCrLf & _
                            "选择'是'：记录所有操作详情" & vbCrLf & _
                            "选择'否'：只记录错误信息", vbYesNo + vbQuestion, "日志记录设置")

    EnableLogging = (loggingResponse = vbYes)

    ' 5. 显示最终设置并确认
    Dim finalSettings As String
    finalSettings = "设置配置完成：" & vbCrLf & vbCrLf & _
                   "检查间隔：" & IntervalMinutes & " 分钟" & vbCrLf & _
                   "工作时间限制：" & IIf(EnableWorkingHoursOnly, "是 (" & WorkingHourStart & ":00-" & WorkingHourEnd & ":00)", "否") & vbCrLf & _
                   "日志记录：" & IIf(EnableLogging, "是", "否") & vbCrLf & vbCrLf & _
                   "设置将在下次启动定时器时生效。"

    MsgBox finalSettings, vbInformation, "配置完成"

    ' 记录配置更改
    Call WriteLog("=== 配置设置已更新 ===")
    Call WriteLog("检查间隔：" & IntervalMinutes & " 分钟")
    Call WriteLog("工作时间限制：" & IIf(EnableWorkingHoursOnly, "是 (" & WorkingHourStart & ":00-" & WorkingHourEnd & ":00)", "否"))
    Call WriteLog("日志记录：" & IIf(EnableLogging, "是", "否"))

    Exit Sub

ErrorHandler:
    MsgBox "配置设置时发生错误：" & Err.Description, vbCritical, "配置错误"
    Call WriteLog("配置设置错误：" & Err.Description)
End Sub

' 显示当前状态和统计信息
Public Sub ShowStatus()
    On Error GoTo ErrorHandler

    Dim statusMsg As String
    Dim draftCount As Long

    ' 获取草稿邮件数量
    If InitializeOutlookObjects() Then
        draftCount = objDraftsFolder.Items.Count
    Else
        draftCount = -1 ' 无法获取
    End If

    statusMsg = "=== Outlook 自动发送状态 ===" & vbCrLf & vbCrLf & _
               "定时器状态：" & IIf(IsTimerRunning, "运行中", "已停止") & vbCrLf & _
               "检查间隔：" & GetIntervalMinutes() & " 分钟" & vbCrLf & _
               "工作时间限制：" & IIf(GetWorkingHoursOnly(), "是 (" & GetWorkingHourStart() & ":00-" & GetWorkingHourEnd() & ":00)", "否") & vbCrLf & _
               "日志记录：" & IIf(GetLoggingEnabled(), "是", "否") & vbCrLf & vbCrLf

    If IsTimerRunning Then
        statusMsg = statusMsg & "上次检查：" & Format(LastCheckTime, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
                   "下次检查：" & Format(DateAdd("n", IntervalMinutes, LastCheckTime), "yyyy-mm-dd hh:mm:ss") & vbCrLf & vbCrLf
    End If

    statusMsg = statusMsg & "当前草稿邮件：" & IIf(draftCount >= 0, CStr(draftCount) & " 封", "无法获取") & vbCrLf & _
               "已发送邮件：" & TotalEmailsSent & " 封" & vbCrLf & _
               "发送失败：" & TotalEmailsFailed & " 封" & vbCrLf & vbCrLf & _
               "当前时间：" & Format(Now, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
               "工作时间状态：" & IIf(IsWorkingHours(), "是", "否")

    MsgBox statusMsg, vbInformation, "系统状态"

    Exit Sub

ErrorHandler:
    MsgBox "获取状态信息时发生错误：" & Err.Description, vbCritical, "状态错误"
End Sub

' ========================================================================
' 内部核心功能函数
' ========================================================================

' 模块初始化
Private Sub InitializeModule()
    ' 初始化默认设置
    If IntervalMinutes = 0 Then IntervalMinutes = DEFAULT_INTERVAL_MINUTES
    If WorkingHourStart = 0 Then WorkingHourStart = 9
    If WorkingHourEnd = 0 Then WorkingHourEnd = 18

    ' 默认启用日志记录
    EnableLogging = True

    ' 重置统计计数器
    TotalEmailsSent = 0
    TotalEmailsFailed = 0

    Call WriteLog("模块初始化完成")
End Sub

' 初始化Outlook对象
Private Function InitializeOutlookObjects() As Boolean
    On Error GoTo ErrorHandler

    ' 获取Outlook应用程序对象
    If objOutlookApp Is Nothing Then
        Set objOutlookApp = Application
    End If

    If objNamespace Is Nothing Then
        Set objNamespace = objOutlookApp.GetNamespace("MAPI")
    End If

    ' 获取草稿箱和已发送邮件文件夹
    If objDraftsFolder Is Nothing Then
        Set objDraftsFolder = objNamespace.GetDefaultFolder(olFolderDrafts)
    End If

    If objSentItemsFolder Is Nothing Then
        Set objSentItemsFolder = objNamespace.GetDefaultFolder(olFolderSentMail)
    End If

    ' 验证所有对象都已正确初始化
    If objOutlookApp Is Nothing Or objNamespace Is Nothing Or _
       objDraftsFolder Is Nothing Or objSentItemsFolder Is Nothing Then
        InitializeOutlookObjects = False
    Else
        InitializeOutlookObjects = True
    End If

    Exit Function

ErrorHandler:
    InitializeOutlookObjects = False
    Call WriteLog("初始化Outlook对象错误：" & Err.Description & " (错误号：" & Err.Number & ")")
End Function

' 启动定时器
Private Sub StartTimer()
    On Error GoTo ErrorHandler

    ' 停止现有定时器（如果有）
    Call StopTimer

    ' 启动新定时器 - 使用Windows API SetTimer函数
    TimerID = SetTimer(0, 0, IntervalMinutes * 60 * 1000, AddressOf TimerProc)
    IsTimerRunning = (TimerID <> 0)

    If Not IsTimerRunning Then
        Call WriteLog("定时器启动失败")
    Else
        Call WriteLog("定时器启动成功，ID：" & TimerID)
    End If

    Exit Sub

ErrorHandler:
    IsTimerRunning = False
    Call WriteLog("启动定时器时发生错误：" & Err.Description)
End Sub

' 停止定时器
Private Sub StopTimer()
    On Error Resume Next

    If TimerID <> 0 Then
        Call KillTimer(0, TimerID)
        Call WriteLog("定时器已停止，ID：" & TimerID)
        TimerID = 0
    End If
    IsTimerRunning = False
End Sub

' 处理草稿邮件（核心发送逻辑）
Private Sub ProcessDraftEmails()
    On Error GoTo ErrorHandler

    Dim draftItems As Outlook.Items
    Dim mailItem As Outlook.MailItem
    Dim i As Long
    Dim sentCount As Long
    Dim errorCount As Long
    Dim skippedCount As Long
    Dim startTime As Date

    startTime = Now
    LastCheckTime = startTime

    ' 检查工作时间限制
    If EnableWorkingHoursOnly And Not IsWorkingHours() Then
        Call WriteLog("当前不在工作时间 (" & Format(Now, "hh:mm") & ")，跳过发送")
        Exit Sub
    End If

    ' 重新初始化对象以确保连接有效
    If Not InitializeOutlookObjects() Then
        Call WriteLog("无法连接到Outlook，跳过本次检查")
        Exit Sub
    End If

    Set draftItems = objDraftsFolder.Items
    sentCount = 0
    errorCount = 0
    skippedCount = 0

    Call WriteLog("开始处理草稿邮件，共 " & draftItems.Count & " 封")

    ' 如果没有草稿邮件，直接退出
    If draftItems.Count = 0 Then
        Call WriteLog("草稿箱为空，无需处理")
        Exit Sub
    End If

    ' 从后往前遍历，避免删除项目时的索引问题
    For i = draftItems.Count To 1 Step -1
        If TypeOf draftItems(i) Is Outlook.MailItem Then
            Set mailItem = draftItems(i)

            ' 验证邮件是否可以发送
            If ValidateEmailForSending(mailItem) Then
                ' 尝试发送邮件
                If SendSingleEmail(mailItem) Then
                    sentCount = sentCount + 1
                    TotalEmailsSent = TotalEmailsSent + 1
                Else
                    errorCount = errorCount + 1
                    TotalEmailsFailed = TotalEmailsFailed + 1
                End If
            Else
                skippedCount = skippedCount + 1
                Call WriteLog("跳过无效邮件：" & GetEmailSummary(mailItem))
            End If
        End If

        ' 释放对象引用
        Set mailItem = Nothing
    Next i

    ' 记录处理结果
    Dim processingTime As Double
    processingTime = (Now - startTime) * 24 * 60 * 60 ' 转换为秒

    Dim resultMsg As String
    resultMsg = "草稿处理完成 - 成功：" & sentCount & " 封，失败：" & errorCount & " 封，跳过：" & skippedCount & " 封，耗时：" & Format(processingTime, "0.0") & " 秒"
    Call WriteLog(resultMsg)

    ' 只有在手动执行或有实际操作时才显示消息框
    If sentCount > 0 Or errorCount > 0 Then
        ' 检查是否为手动执行（通过调用堆栈判断）
        If InStr(1, Application.VBE.ActiveCodePane.CodeModule.Name, "ManualSendDrafts") > 0 Then
            MsgBox resultMsg, vbInformation, "处理结果"
        End If
    End If

    ' 清理对象引用
    Set draftItems = Nothing

    Exit Sub

ErrorHandler:
    Call WriteLog("处理草稿邮件时发生严重错误：" & Err.Description & " (错误号：" & Err.Number & ")")

    ' 尝试清理对象引用
    Set mailItem = Nothing
    Set draftItems = Nothing
End Sub

' 验证邮件是否可以发送
Private Function ValidateEmailForSending(ByRef mailItem As Outlook.MailItem) As Boolean
    On Error GoTo ErrorHandler

    ' 检查收件人
    If Trim(mailItem.To) = "" And Trim(mailItem.CC) = "" And Trim(mailItem.BCC) = "" Then
        ValidateEmailForSending = False
        Exit Function
    End If

    ' 检查主题（可选验证）
    If Trim(mailItem.Subject) = "" Then
        Call WriteLog("警告：邮件无主题 - " & GetEmailSummary(mailItem))
    End If

    ' 检查邮件状态
    If mailItem.Sent Then
        Call WriteLog("跳过已发送邮件：" & GetEmailSummary(mailItem))
        ValidateEmailForSending = False
        Exit Function
    End If

    ValidateEmailForSending = True
    Exit Function

ErrorHandler:
    ValidateEmailForSending = False
    Call WriteLog("验证邮件时发生错误：" & Err.Description)
End Function

' 发送单封邮件
Private Function SendSingleEmail(ByRef mailItem As Outlook.MailItem) As Boolean
    On Error GoTo ErrorHandler

    Dim emailSummary As String
    emailSummary = GetEmailSummary(mailItem)

    ' 记录发送尝试
    Call WriteLog("尝试发送邮件：" & emailSummary)

    ' 发送邮件
    mailItem.Send

    ' 发送成功
    Call WriteLog("✓ 成功发送邮件：" & emailSummary)
    SendSingleEmail = True
    Exit Function

ErrorHandler:
    ' 发送失败
    Call WriteLog("✗ 发送邮件失败：" & emailSummary & " - " & Err.Description & " (错误号：" & Err.Number & ")")
    SendSingleEmail = False
End Function

' 获取邮件摘要信息
Private Function GetEmailSummary(ByRef mailItem As Outlook.MailItem) As String
    On Error Resume Next

    Dim summary As String
    Dim recipients As String

    ' 构建收件人列表
    If Trim(mailItem.To) <> "" Then recipients = recipients & "To:" & Trim(mailItem.To)
    If Trim(mailItem.CC) <> "" Then recipients = recipients & " CC:" & Trim(mailItem.CC)
    If Trim(mailItem.BCC) <> "" Then recipients = recipients & " BCC:" & Trim(mailItem.BCC)

    ' 限制长度
    If Len(recipients) > 100 Then recipients = Left(recipients, 97) & "..."

    summary = """" & Trim(mailItem.Subject) & """ -> " & recipients
    GetEmailSummary = summary
End Function

' 检查是否在工作时间
Private Function IsWorkingHours() As Boolean
    Dim currentHour As Integer
    currentHour = Hour(Now)

    ' 使用配置的工作时间
    If WorkingHourStart < WorkingHourEnd Then
        ' 正常情况：如 9-18
        IsWorkingHours = (currentHour >= WorkingHourStart And currentHour < WorkingHourEnd)
    Else
        ' 跨夜情况：如 22-6
        IsWorkingHours = (currentHour >= WorkingHourStart Or currentHour < WorkingHourEnd)
    End If
End Function

' ========================================================================
' 辅助功能函数
' ========================================================================

' 写入日志
Private Sub WriteLog(ByVal message As String)
    ' 始终记录错误和重要信息
    If Not EnableLogging And InStr(message, "错误") = 0 And InStr(message, "失败") = 0 And InStr(message, "===") = 0 Then
        Exit Sub
    End If

    On Error Resume Next

    Dim fileNum As Integer
    Dim logMessage As String
    Dim logDir As String

    fileNum = FreeFile
    logMessage = Format(Now, "yyyy-mm-dd hh:mm:ss") & " [PID:" & GetCurrentProcessId() & "] " & message

    ' 确保日志目录存在
    logDir = "C:\Temp"
    If Dir(logDir, vbDirectory) = "" Then
        MkDir logDir
    End If

    ' 写入日志文件
    Open LOG_FILE_PATH For Append As #fileNum
    Print #fileNum, logMessage
    Close #fileNum

    ' 同时输出到调试窗口（开发时有用）
    Debug.Print logMessage
End Sub

' 获取当前进程ID（用于日志标识）
#If VBA7 Then
    Private Declare PtrSafe Function GetCurrentProcessId Lib "kernel32" () As Long
#Else
    Private Declare Function GetCurrentProcessId Lib "kernel32" () As Long
#End If

' ========================================================================
' 配置访问函数（提供安全的配置访问）
' ========================================================================

Private Function GetIntervalMinutes() As Long
    If IntervalMinutes <= 0 Then IntervalMinutes = DEFAULT_INTERVAL_MINUTES
    GetIntervalMinutes = IntervalMinutes
End Function

Private Function GetWorkingHoursOnly() As Boolean
    GetWorkingHoursOnly = EnableWorkingHoursOnly
End Function

Private Function GetLoggingEnabled() As Boolean
    GetLoggingEnabled = EnableLogging
End Function

Private Function GetWorkingHourStart() As Integer
    If WorkingHourStart <= 0 Then WorkingHourStart = 9
    GetWorkingHourStart = WorkingHourStart
End Function

Private Function GetWorkingHourEnd() As Integer
    If WorkingHourEnd <= 0 Then WorkingHourEnd = 18
    GetWorkingHourEnd = WorkingHourEnd
End Function

' ========================================================================
' 定时器回调函数
' ========================================================================

' 定时器回调函数
#If VBA7 Then
    Public Sub TimerProc(ByVal hwnd As LongPtr, ByVal uMsg As Long, ByVal idEvent As LongPtr, ByVal dwTime As Long)
#Else
    Public Sub TimerProc(ByVal hwnd As Long, ByVal uMsg As Long, ByVal idEvent As Long, ByVal dwTime As Long)
#End If
    On Error GoTo ErrorHandler

    ' 验证定时器ID
    If idEvent <> TimerID Then Exit Sub

    ' 在定时器回调中执行草稿发送
    Call ProcessDraftEmails

    Exit Sub

ErrorHandler:
    Call WriteLog("定时器回调错误：" & Err.Description)
End Sub

' ========================================================================
' 模块清理和终止函数
' ========================================================================

' 清理所有资源
Public Sub CleanupModule()
    On Error Resume Next

    ' 停止定时器
    Call StopTimer

    ' 清理对象引用
    Set objOutlookApp = Nothing
    Set objNamespace = Nothing
    Set objDraftsFolder = Nothing
    Set objSentItemsFolder = Nothing

    ' 重置变量
    IsTimerRunning = False
    TimerID = 0
    TotalEmailsSent = 0
    TotalEmailsFailed = 0

    Call WriteLog("模块资源已清理")
End Sub

' 模块终止时的清理（自动调用）
Private Sub Class_Terminate()
    Call CleanupModule
End Sub

' ========================================================================
' 快速测试和调试函数
' ========================================================================

' 测试模块功能
Public Sub TestModule()
    On Error GoTo ErrorHandler

    MsgBox "开始测试模块功能...", vbInformation, "测试开始"

    ' 测试Outlook连接
    If InitializeOutlookObjects() Then
        MsgBox "✓ Outlook连接正常" & vbCrLf & _
               "草稿邮件数量：" & objDraftsFolder.Items.Count, vbInformation, "连接测试"
    Else
        MsgBox "✗ Outlook连接失败", vbCritical, "连接测试"
        Exit Sub
    End If

    ' 测试日志功能
    Call WriteLog("=== 模块测试 ===")
    Call WriteLog("测试日志功能正常")

    ' 测试工作时间判断
    MsgBox "当前时间：" & Format(Now, "yyyy-mm-dd hh:mm:ss") & vbCrLf & _
           "是否工作时间：" & IIf(IsWorkingHours(), "是", "否") & vbCrLf & _
           "工作时间设置：" & GetWorkingHourStart() & ":00-" & GetWorkingHourEnd() & ":00", vbInformation, "时间测试"

    MsgBox "模块功能测试完成！", vbInformation, "测试完成"

    Exit Sub

ErrorHandler:
    MsgBox "测试过程中发生错误：" & Err.Description, vbCritical, "测试错误"
End Sub
